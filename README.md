# Телеграм бот с ИИ на базе Cerebras AI

Минималистичный телеграм бот с поддержкой текстовых сообщений и изображений.

## Возможности

- 💬 Обработка текстовых сообщений через Cerebras AI
- 🖼️ Анализ и описание изображений
- 🧠 Поддержка контекста разговора (последние 20 сообщений)
- 🔄 Кнопка сброса контекста
- ⚡ Быстрые ответы с использованием модели qwen-3-235b-a22b-instruct-2507

## Установка и запуск

1. Установите зависимости:
```bash
pip install -r requirements.txt
```

2. Запустите бота:
```bash
python telegram_bot.py
```

## Команды бота

- `/start` или `/help` - показать приветственное сообщение
- Отправьте текстовое сообщение - получите ответ от ИИ
- Отправьте фото - получите описание изображения
- Нажмите кнопку "🔄 Сбросить контекст" - очистите историю разговора

## Технические детали

- **Библиотека**: pyTelegramBotAPI (telebot)
- **ИИ модель**: qwen-3-235b-a22b-instruct-2507 (Cerebras AI)
- **Обработка изображений**: PIL (Pillow)
- **Максимум токенов**: 8192
- **Temperature**: 0.5
- **Top_p**: 0.8

Бот автоматически изменяет размер изображений до 800x800 пикселей для оптимизации использования токенов.
