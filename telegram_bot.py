#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import telebot
import requests
import json
import base64
from io import BytesIO
from PIL import Image
from telebot.types import InlineKeyboardMarkup, InlineKeyboardButton

# Конфигурация
BOT_TOKEN = "7634877289:AAHjNJBUxyQGm0n42KzJ3LF1X3a4rCWwpGM"
CEREBRAS_API_KEY = "csk-8fmmhwwyyhvj22htt9xyc9jccvxecmcyd4jkdmtd3wrdvvem"
CEREBRAS_API_URL = "https://api.cerebras.ai/v1/chat/completions"

# Инициализация бота
bot = telebot.TeleBot(BOT_TOKEN)

# Хранение контекста для каждого пользователя
user_contexts = {}

def get_user_context(user_id):
    """Получить контекст пользователя"""
    if user_id not in user_contexts:
        user_contexts[user_id] = []
    return user_contexts[user_id]

def add_to_context(user_id, role, content):
    """Добавить сообщение в контекст пользователя"""
    context = get_user_context(user_id)
    context.append({"role": role, "content": content})
    
    # Ограничиваем контекст последними 20 сообщениями
    if len(context) > 20:
        context.pop(0)

def clear_context(user_id):
    """Очистить контекст пользователя"""
    user_contexts[user_id] = []

def create_reset_keyboard():
    """Создать клавиатуру с кнопкой сброса контекста"""
    keyboard = InlineKeyboardMarkup()
    reset_button = InlineKeyboardButton("🔄 Сбросить контекст", callback_data="reset_context")
    keyboard.add(reset_button)
    return keyboard

def call_cerebras_api(messages):
    """Вызов Cerebras AI API"""
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {CEREBRAS_API_KEY}"
    }
    
    data = {
        "model": "qwen-3-235b-a22b-instruct-2507",
        "stream": False,
        "max_tokens": 8192,
        "temperature": 0.5,
        "top_p": 0.8,
        "messages": messages
    }
    
    try:
        response = requests.post(CEREBRAS_API_URL, headers=headers, json=data, timeout=60)
        response.raise_for_status()
        
        result = response.json()
        return result["choices"][0]["message"]["content"]
    
    except requests.exceptions.RequestException as e:
        return f"Ошибка API: {str(e)}"
    except KeyError as e:
        return f"Ошибка обработки ответа: {str(e)}"
    except Exception as e:
        return f"Неожиданная ошибка: {str(e)}"

def image_to_base64(image_path):
    """Конвертировать изображение в base64"""
    try:
        with Image.open(image_path) as img:
            # Изменяем размер изображения для экономии токенов
            img.thumbnail((800, 800), Image.Resampling.LANCZOS)
            
            buffer = BytesIO()
            img.save(buffer, format="JPEG", quality=85)
            img_str = base64.b64encode(buffer.getvalue()).decode()
            return img_str
    except Exception as e:
        print(f"Ошибка обработки изображения: {e}")
        return None

@bot.message_handler(commands=['start', 'help'])
def send_welcome(message):
    """Обработчик команд /start и /help"""
    welcome_text = """
🤖 Привет! Я ИИ-бот на базе Cerebras AI.

Я могу:
• Отвечать на текстовые вопросы
• Анализировать изображения
• Поддерживать контекст разговора

Просто отправь мне сообщение или фото!
    """
    bot.reply_to(message, welcome_text, reply_markup=create_reset_keyboard())

@bot.message_handler(content_types=['photo'])
def handle_photo(message):
    """Обработчик фотографий"""
    try:
        # Получаем файл фотографии
        file_info = bot.get_file(message.photo[-1].file_id)
        downloaded_file = bot.download_file(file_info.file_path)
        
        # Сохраняем временно
        temp_path = f"temp_{message.from_user.id}.jpg"
        with open(temp_path, 'wb') as new_file:
            new_file.write(downloaded_file)
        
        # Конвертируем в base64
        img_base64 = image_to_base64(temp_path)
        
        if img_base64:
            # Получаем контекст пользователя
            context = get_user_context(message.from_user.id)
            
            # Добавляем системное сообщение для анализа изображения
            messages = [
                {"role": "system", "content": "Ты помощник, который анализирует изображения. Опиши что видишь на изображении подробно и интересно."}
            ] + context + [
                {"role": "user", "content": f"Проанализируй это изображение: data:image/jpeg;base64,{img_base64}"}
            ]
            
            # Отправляем запрос к AI
            bot.send_chat_action(message.chat.id, 'typing')
            ai_response = call_cerebras_api(messages)
            
            # Добавляем в контекст
            add_to_context(message.from_user.id, "user", "Отправил изображение")
            add_to_context(message.from_user.id, "assistant", ai_response)
            
            # Отправляем ответ
            bot.reply_to(message, ai_response, reply_markup=create_reset_keyboard())
        else:
            bot.reply_to(message, "Ошибка обработки изображения. Попробуйте еще раз.")
            
        # Удаляем временный файл
        import os
        try:
            os.remove(temp_path)
        except:
            pass
            
    except Exception as e:
        bot.reply_to(message, f"Ошибка при обработке фото: {str(e)}")

@bot.message_handler(func=lambda message: True)
def handle_text(message):
    """Обработчик текстовых сообщений"""
    try:
        user_id = message.from_user.id
        user_message = message.text
        
        # Получаем контекст пользователя
        context = get_user_context(user_id)
        
        # Формируем сообщения для API
        messages = [
            {"role": "system", "content": "Ты полезный ИИ-помощник. Отвечай дружелюбно и информативно."}
        ] + context + [
            {"role": "user", "content": user_message}
        ]
        
        # Отправляем запрос к AI
        bot.send_chat_action(message.chat.id, 'typing')
        ai_response = call_cerebras_api(messages)
        
        # Добавляем в контекст
        add_to_context(user_id, "user", user_message)
        add_to_context(user_id, "assistant", ai_response)
        
        # Отправляем ответ
        bot.reply_to(message, ai_response, reply_markup=create_reset_keyboard())
        
    except Exception as e:
        bot.reply_to(message, f"Ошибка при обработке сообщения: {str(e)}")

@bot.callback_query_handler(func=lambda call: call.data == "reset_context")
def handle_reset_context(call):
    """Обработчик кнопки сброса контекста"""
    user_id = call.from_user.id
    clear_context(user_id)
    
    bot.answer_callback_query(call.id, "Контекст сброшен! 🔄")
    bot.send_message(call.message.chat.id, "✅ Контекст разговора очищен. Начинаем заново!")

if __name__ == "__main__":
    print("🤖 Бот запущен...")
    try:
        bot.infinity_polling(none_stop=True)
    except Exception as e:
        print(f"Ошибка запуска бота: {e}")
